# Complete Cursor 0.45.14 Removal Guide for Windows 11

## Method 1: Using Windows Settings (Recommended First Try)

1. **Open Windows Settings**
   - Press `Win + I` or click Start → Settings
   - Go to "Apps" → "Installed apps"
   - Search for "Cursor" in the search box
   - Click the three dots (...) next to Cursor
   - Select "Uninstall" and follow prompts

## Method 2: Using Control Panel

1. **Open Control Panel**
   - Press `Win + R`, type `appwiz.cpl`, press Enter
   - Or search "Control Panel" → "Programs and Features"
   - Find "Cursor" in the list
   - Right-click → "Uninstall"

## Method 3: Manual Removal (When Standard Methods Fail)

### Step 1: Stop Cursor Processes
1. Press `Ctrl + Shift + Esc` to open Task Manager
2. Look for any Cursor-related processes
3. Right-click each → "End task"

### Step 2: Delete Installation Folders
Delete these folders if they exist:
- `C:\Users\<USER>\AppData\Local\Programs\cursor`
- `C:\Users\<USER>\AppData\Roaming\Cursor`
- `C:\Users\<USER>\AppData\Local\cursor`
- `C:\Program Files\Cursor`
- `C:\Program Files (x86)\Cursor`

### Step 3: Clean Registry (Advanced Users Only)
⚠️ **Warning**: Always backup registry before editing!

1. Press `Win + R`, type `regedit`, press Enter
2. Navigate to and delete these keys if they exist:
   - `HKEY_CURRENT_USER\Software\Cursor`
   - `HKEY_LOCAL_MACHINE\SOFTWARE\Cursor`
   - `HKEY_LOCAL_MACHINE\SOFTWARE\WOW6432Node\Cursor`

3. Search for Cursor entries in:
   - `HKEY_CURRENT_USER\Software\Microsoft\Windows\CurrentVersion\Uninstall`
   - `HKEY_LOCAL_MACHINE\SOFTWARE\Microsoft\Windows\CurrentVersion\Uninstall`

### Step 4: Remove Desktop/Start Menu Shortcuts
- Delete Cursor shortcuts from Desktop
- Remove from Start Menu: `C:\Users\<USER>\AppData\Roaming\Microsoft\Windows\Start Menu\Programs`

## Method 4: Using Third-Party Uninstaller Tools

If manual removal seems complex, use these tools:
- **Revo Uninstaller** (Free/Pro)
- **IObit Uninstaller** (Free)
- **Geek Uninstaller** (Free)

These tools can:
- Force uninstall stubborn programs
- Clean leftover files and registry entries
- Scan for remnants after uninstallation

## Method 5: PowerShell Command (Windows 11)

1. Right-click Start button → "Windows Terminal (Admin)"
2. Run this command to list installed apps:
   ```powershell
   Get-AppxPackage | Where-Object {$_.Name -like "*Cursor*"}
   ```
3. If found, uninstall with:
   ```powershell
   Get-AppxPackage *Cursor* | Remove-AppxPackage
   ```

## Troubleshooting Tips

### If "Access Denied" Errors Occur:
1. Run Command Prompt as Administrator
2. Take ownership of folders:
   ```cmd
   takeown /f "C:\Path\To\Cursor\Folder" /r /d y
   icacls "C:\Path\To\Cursor\Folder" /grant administrators:F /t
   ```

### If Files Are "In Use":
1. Boot into Safe Mode:
   - Hold Shift while clicking Restart
   - Choose Troubleshoot → Advanced → Startup Settings → Restart
   - Press 4 for Safe Mode
2. Delete files in Safe Mode

### Clean Temporary Files:
1. Press `Win + R`, type `%temp%`, press Enter
2. Delete any Cursor-related temporary files
3. Empty Recycle Bin

## Final Verification

After removal, verify it's completely gone:
1. Search Windows for "Cursor" - should find nothing
2. Check if any Cursor processes remain in Task Manager
3. Restart your computer
4. Confirm no Cursor shortcuts or files remain

## Prevention for Future

- Always use official uninstallers first
- Keep installation files for proper uninstallation
- Consider using portable versions when available
- Regular system maintenance with tools like CCleaner

---

**Note**: The error you encountered (`Windows cannot find 'C:\Users\<USER>\AppData\Local\Programs\cursor\Uninstall Cursor.exe'`) indicates the uninstaller file is missing or corrupted. This is why manual removal methods above are necessary.
