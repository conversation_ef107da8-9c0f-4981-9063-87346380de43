# Bolt - AI-Powered Development Environment

[![Bolt Open Source Codebase](./public/social_preview_index.jpg)](https://bolt.new)

> Welcome to the **Bolt** open-source codebase! This repository contains the core components from bolt.new to help you build **AI-powered software development tools** powered by StackBlitz's **WebContainer API**.

## Overview

Bolt is an innovative AI development tool that enables users to prompt, run, edit, and deploy full-stack web applications directly in the browser. It combines the power of:

- [WebContainer API](https://webcontainers.io/api) - For running full-stack Node.js environments in the browser
- [Claude Sonnet 3.5](https://www.anthropic.com/news/claude-3-5-sonnet) - For AI capabilities
- [Remix](https://remix.run/) - For the web application framework
- [AI SDK](https://sdk.vercel.ai/) - For AI model integration

### Key Features

- Browser-based development environment
- Real-time AI assistance
- Full Node.js environment support
- File system and package manager access
- Interactive terminal
- Modern and responsive UI
- Theme customization
- Chat history management

## Prerequisites

Before you begin, ensure you have the following installed:
- Node.js (v20.15.1 or later)
- pnpm (v9.4.0)

## Getting Started

1. Clone the repository:
\`\`\`bash
git clone https://github.com/stackblitz/bolt.new.git
\`\`\`

2. Install dependencies:
\`\`\`bash
pnpm install
\`\`\`

3. Create a \`.env.local\` file in the root directory:
\`\`\`
ANTHROPIC_API_KEY=your_api_key_here
\`\`\`

Optional: Set debug level
\`\`\`
VITE_LOG_LEVEL=debug
\`\`\`

## Available Scripts

- \`pnpm run dev\` - Start the development server
- \`pnpm run build\` - Build the project
- \`pnpm run start\` - Run the built application locally using Wrangler Pages
- \`pnpm run preview\` - Build and start locally (production build testing)
- \`pnpm test\` - Run tests using Vitest
- \`pnpm run typecheck\` - Run TypeScript type checking
- \`pnpm run typegen\` - Generate TypeScript types using Wrangler
- \`pnpm run deploy\` - Build and deploy to Cloudflare Pages

## Project Structure

- \`/app\` - Main application code
  - \`/components\` - React components
  - \`/lib\` - Core libraries and utilities
  - \`/routes\` - Application routes
  - \`/styles\` - Global styles and themes
  - \`/types\` - TypeScript type definitions
- \`/functions\` - Serverless functions
- \`/public\` - Static assets

## Technologies Used

- **Frontend**: React, TypeScript, SCSS
- **Framework**: Remix
- **AI Integration**: AI SDK, Claude Sonnet 3.5
- **Development Environment**: WebContainer API
- **Deployment**: Cloudflare Pages and Workers

## License

MIT

## Contributing

See [CONTRIBUTING.md](./CONTRIBUTING.md) for detailed information about contributing to this project.

## Important Notes

- Never commit your \`.env.local\` file to version control
- HTTP streaming might not work as expected with \`wrangler pages dev\`
- Commercial usage of WebContainer API requires appropriate licensing

For more information about Bolt and WebContainer API commercial usage, visit [StackBlitz Pricing](https://stackblitz.com/pricing#webcontainer-api).